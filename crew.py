import os
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

from crewai import Agent, Crew, Process, Task
from crewai.project import CrewBase, agent, crew, task
from langchain_google_genai import ChatGoogleGenerativeAI

from tools.job_scraper_tools import JobSearchTool, JobDataExtractorTool, JobFilterTool

# Load environment variables
load_dotenv()


@CrewBase
class JobScrapingCrew:
    """Job Scraping and Analysis Crew"""
    
    def __init__(self):
        # Initialize Gemini LLM
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            temperature=0.1,
            verbose=True
        )
        
        # Initialize tools
        self.job_search_tool = JobSearchTool()
        self.job_extractor_tool = JobDataExtractorTool()
        self.job_filter_tool = JobFilterTool()

    @agent
    def job_search_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['job_search_agent'],
            tools=[self.job_search_tool],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )

    @agent
    def data_extraction_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['data_extraction_agent'],
            tools=[self.job_extractor_tool],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )

    @agent
    def filter_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['filter_agent'],
            tools=[self.job_filter_tool],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )

    @agent
    def analysis_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['analysis_agent'],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )

    @agent
    def report_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['report_agent'],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )

    @task
    def job_search_task(self) -> Task:
        return Task(
            config=self.tasks_config['job_search_task'],
            agent=self.job_search_agent(),
            tools=[self.job_search_tool]
        )

    @task
    def data_extraction_task(self) -> Task:
        return Task(
            config=self.tasks_config['data_extraction_task'],
            agent=self.data_extraction_agent(),
            tools=[self.job_extractor_tool],
            context=[self.job_search_task()]
        )

    @task
    def filter_task(self) -> Task:
        return Task(
            config=self.tasks_config['filter_task'],
            agent=self.filter_agent(),
            tools=[self.job_filter_tool],
            context=[self.data_extraction_task()]
        )

    @task
    def analysis_task(self) -> Task:
        return Task(
            config=self.tasks_config['analysis_task'],
            agent=self.analysis_agent(),
            context=[self.filter_task()]
        )

    @task
    def report_generation_task(self) -> Task:
        return Task(
            config=self.tasks_config['report_generation_task'],
            agent=self.report_agent(),
            context=[self.analysis_task()],
            output_file='career_opportunities_report.md'
        )

    @crew
    def crew(self) -> Crew:
        """Creates the Job Scraping crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            memory=True,
            planning=True,
            planning_llm=self.llm
        )


def run_job_scraping_crew():
    """
    Run the job scraping crew with specific parameters
    """
    # Initialize the crew
    job_crew = JobScrapingCrew()
    
    # Define search parameters
    inputs = {
        'search_terms': 'software developer python javascript',
        'experience_level': '1-3 years',
        'location': 'remote',
        'target_roles': [
            'Software Developer',
            'Frontend Developer', 
            'Backend Developer',
            'Full Stack Developer',
            'Python Developer',
            'JavaScript Developer',
            'Data Analyst',
            'Junior Developer'
        ],
        'preferred_skills': [
            'Python', 'JavaScript', 'React', 'Node.js', 
            'SQL', 'Git', 'AWS', 'Docker'
        ],
        'max_results': 50
    }
    
    print("🚀 Starting Job Scraping and Analysis Crew...")
    print(f"Search Terms: {inputs['search_terms']}")
    print(f"Experience Level: {inputs['experience_level']}")
    print(f"Location: {inputs['location']}")
    print(f"Max Results: {inputs['max_results']}")
    print("-" * 50)
    
    try:
        # Execute the crew
        result = job_crew.crew().kickoff(inputs=inputs)
        
        print("\n✅ Job scraping and analysis completed successfully!")
        print(f"📄 Report generated: career_opportunities_report.md")
        
        return result
        
    except Exception as e:
        print(f"❌ Error running job scraping crew: {str(e)}")
        return None


if __name__ == "__main__":
    # Run the crew
    result = run_job_scraping_crew()
    
    if result:
        print("\n🎉 Job scraping mission accomplished!")
        print("Check the generated report for detailed findings.")
    else:
        print("\n💥 Mission failed. Please check the logs for errors.")
