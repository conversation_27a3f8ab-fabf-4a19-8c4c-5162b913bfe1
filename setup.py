#!/usr/bin/env python3
"""
Setup script for Job Scraping Agentic System
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["tools", "config", "output", "logs"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")
        else:
            print(f"✅ Directory already exists: {directory}")

def check_environment():
    """Check environment setup"""
    print("🔍 Checking environment setup...")
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("⚠️  .env file not found. Creating template...")
        with open(".env", "w") as f:
            f.write("# Google Gemini API Key\n")
            f.write("GOOGLE_API_KEY=your_api_key_here\n")
            f.write("\n# Optional: Add other API keys as needed\n")
            f.write("# SERPER_API_KEY=your_serper_key_here\n")
        print("📝 .env template created. Please add your API keys.")
        return False
    
    # Check if API key is set
    from dotenv import load_dotenv
    load_dotenv()
    
    if not os.getenv("GOOGLE_API_KEY") or os.getenv("GOOGLE_API_KEY") == "your_api_key_here":
        print("⚠️  GOOGLE_API_KEY not properly set in .env file")
        return False
    
    print("✅ Environment configuration looks good!")
    return True

def main():
    """Main setup function"""
    print("=" * 60)
    print("🛠️  JOB SCRAPING AGENTIC SYSTEM SETUP")
    print("=" * 60)
    
    # Create directories
    create_directories()
    print()
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during package installation.")
        return
    print()
    
    # Check environment
    env_ok = check_environment()
    print()
    
    if env_ok:
        print("🎉 Setup completed successfully!")
        print("You can now run the system with: python main.py")
    else:
        print("⚠️  Setup completed with warnings.")
        print("Please configure your .env file before running the system.")
    
    print("\n📋 Quick Start Guide:")
    print("1. Ensure your .env file has the correct API keys")
    print("2. Run: python main.py")
    print("3. Follow the prompts to configure your job search")
    print("4. Review the generated report")

if __name__ == "__main__":
    main()
