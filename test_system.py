#!/usr/bin/env python3
"""
Test script for Job Scraping Agentic System
"""

import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """Test environment setup"""
    print("🧪 Testing Environment Setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Check API key
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ GOOGLE_API_KEY not found")
        return False
    elif api_key == "your_api_key_here":
        print("❌ GOOGLE_API_KEY not properly configured")
        return False
    else:
        print("✅ GOOGLE_API_KEY found and configured")
    
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing Imports...")
    
    try:
        import crewai
        print("✅ CrewAI imported successfully")
    except ImportError as e:
        print(f"❌ CrewAI import failed: {e}")
        return False
    
    try:
        import langchain_google_genai
        print("✅ LangChain Google GenAI imported successfully")
    except ImportError as e:
        print(f"❌ LangChain Google GenAI import failed: {e}")
        return False
    
    try:
        from tools.job_scraper_tools import JobSearchTool, JobDataExtractorTool, JobFilterTool
        print("✅ Custom tools imported successfully")
    except ImportError as e:
        print(f"❌ Custom tools import failed: {e}")
        return False
    
    try:
        import requests
        import beautifulsoup4
        print("✅ Web scraping libraries imported successfully")
    except ImportError as e:
        print(f"❌ Web scraping libraries import failed: {e}")
        return False
    
    return True

def test_tools():
    """Test custom tools functionality"""
    print("\n🧪 Testing Custom Tools...")
    
    try:
        from tools.job_scraper_tools import JobSearchTool, JobDataExtractorTool, JobFilterTool
        
        # Test tool initialization
        search_tool = JobSearchTool()
        extractor_tool = JobDataExtractorTool()
        filter_tool = JobFilterTool()
        
        print("✅ All tools initialized successfully")
        
        # Test basic functionality (without actual web requests)
        print("✅ Tools basic functionality verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool testing failed: {e}")
        return False

def test_gemini_connection():
    """Test Gemini API connection"""
    print("\n🧪 Testing Gemini API Connection...")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            temperature=0.1
        )
        
        # Test with a simple prompt
        response = llm.invoke("Hello, this is a test. Please respond with 'Connection successful!'")
        
        if "successful" in response.content.lower():
            print("✅ Gemini API connection successful")
            return True
        else:
            print("⚠️  Gemini API responded but with unexpected content")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API connection failed: {e}")
        return False

def test_crew_configuration():
    """Test CrewAI configuration"""
    print("\n🧪 Testing CrewAI Configuration...")
    
    try:
        from crew import JobScrapingCrew
        
        # Initialize crew
        job_crew = JobScrapingCrew()
        
        # Check if agents are properly configured
        agents = job_crew.agents
        tasks = job_crew.tasks
        
        print(f"✅ Crew initialized with {len(agents)} agents and {len(tasks)} tasks")
        
        return True
        
    except Exception as e:
        print(f"❌ Crew configuration test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("🧪 JOB SCRAPING SYSTEM - COMPREHENSIVE TESTING")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment),
        ("Module Imports", test_imports),
        ("Custom Tools", test_tools),
        ("Gemini API Connection", test_gemini_connection),
        ("CrewAI Configuration", test_crew_configuration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready to use.")
        print("You can now run: python main.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the configuration.")
        print("Run python setup.py to fix common issues.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
