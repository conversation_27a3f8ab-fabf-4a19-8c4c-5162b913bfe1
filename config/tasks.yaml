job_search_task:
  description: >
    Search for job opportunities across multiple platforms including:
    1. Major job boards (Indeed, Glassdoor, Monster, ZipRecruiter)
    2. LinkedIn job postings
    3. Company career pages
    4. Tech-specific platforms (AngelList, Stack Overflow Jobs, GitHub Jobs)
    
    Focus on positions that require 1+ years of experience in the following areas:
    - Software Development (Python, JavaScript, Java, etc.)
    - Data Science and Analytics
    - Digital Marketing
    - Product Management
    - UX/UI Design
    - DevOps and Cloud Engineering
    
    Search parameters:
    - Experience level: 1-3 years
    - Location: Remote, major tech hubs
    - Job type: Full-time, Contract
    - Posted within: Last 7 days
  expected_output: >
    A comprehensive list of job URLs and basic information (title, company, location, 
    platform) for positions matching the criteria. Minimum 50 relevant job opportunities.
  agent: job_search_agent

data_extraction_task:
  description: >
    Extract detailed information from the job URLs identified in the search phase.
    For each job posting, extract:
    
    1. Basic Information:
       - Job title
       - Company name
       - Location (remote/hybrid/onsite)
       - Salary range (if available)
       - Job type (full-time/contract/part-time)
    
    2. Requirements:
       - Years of experience required
       - Required skills and technologies
       - Education requirements
       - Preferred qualifications
    
    3. Job Details:
       - Job description summary
       - Key responsibilities
       - Benefits and perks
       - Application deadline
       - Company size and industry
    
    4. Contact Information:
       - Application method
       - Recruiter/HR contact (if available)
       - Company website
  expected_output: >
    Structured JSON data containing all extracted information for each job posting.
    Data should be clean, consistent, and ready for analysis.
  agent: data_extraction_agent

filter_task:
  description: >
    Filter and categorize the extracted job data based on the following criteria:
    
    1. Experience Requirements:
       - Exactly 1+ years experience
       - 1-2 years experience
       - 1-3 years experience
       - Entry-level with some experience preferred
    
    2. Quality Filters:
       - Remove duplicate postings
       - Filter out obvious scams or low-quality postings
       - Verify company legitimacy
       - Check for complete job descriptions
    
    3. Categorization:
       - Group by industry/field
       - Categorize by experience level
       - Sort by location preferences
       - Tag by remote work options
    
    4. Relevance Scoring:
       - Score based on job description quality
       - Rate based on company reputation
       - Evaluate based on growth potential
  expected_output: >
    Filtered and categorized job data with relevance scores. Include reasoning 
    for filtering decisions and category assignments. Provide statistics on 
    filtering results (e.g., X jobs filtered out, Y jobs categorized).
  agent: filter_agent

analysis_task:
  description: >
    Analyze the filtered job opportunities and provide insights:
    
    1. Market Analysis:
       - Trending skills and technologies
       - Salary ranges by role and location
       - Most active companies hiring
       - Geographic distribution of opportunities
    
    2. Opportunity Evaluation:
       - Rank top 20 opportunities based on multiple factors
       - Identify companies with best growth potential
       - Highlight remote-friendly organizations
       - Find positions with clear career progression
    
    3. Skills Gap Analysis:
       - Most in-demand skills
       - Skills that command higher salaries
       - Emerging technology requirements
       - Certification and education trends
    
    4. Application Strategy:
       - Best platforms for job searching
       - Optimal application timing
       - Companies with faster hiring processes
       - Networking opportunities identification
  expected_output: >
    Comprehensive analysis report with rankings, insights, and strategic 
    recommendations. Include data visualizations concepts and actionable 
    advice for job seekers.
  agent: analysis_agent

report_generation_task:
  description: >
    Generate a comprehensive career opportunities report that includes:
    
    1. Executive Summary:
       - Key findings and opportunities
       - Market overview for 1+ year experience level
       - Top recommendations
    
    2. Detailed Findings:
       - Complete list of filtered opportunities
       - Company profiles and ratings
       - Salary analysis and trends
       - Skills requirements breakdown
    
    3. Strategic Recommendations:
       - Priority applications (top 10-15 opportunities)
       - Skill development suggestions
       - Networking strategies
       - Application timeline and approach
    
    4. Appendices:
       - Complete job listings with details
       - Company research summaries
       - Industry trend analysis
       - Resource links and contacts
    
    Format the report professionally with clear sections, bullet points, 
    and actionable insights. Include both summary and detailed views.
  expected_output: >
    A professional, comprehensive career opportunities report in markdown format 
    with clear sections, actionable insights, and strategic recommendations. 
    The report should be immediately useful for job search planning and execution.
  agent: report_agent
  output_file: career_opportunities_report.md
