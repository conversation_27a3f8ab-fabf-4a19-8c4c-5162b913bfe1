# ruff: noqa: F403, F405
# The contents of this file are automatically written by
# tools/generate_schema_wrapper.py. Do not modify directly.

from altair.vegalite.v5.schema import channels, core
from altair.vegalite.v5.schema.channels import *
from altair.vegalite.v5.schema.core import *

SCHEMA_VERSION = "v5.20.1"

SCHEMA_URL = "https://vega.github.io/schema/vega-lite/v5.20.1.json"

__all__ = [
    "SCHEMA_URL",
    "SCHEMA_VERSION",
    "URI",
    "X2",
    "Y2",
    "Aggregate",
    "AggregateOp",
    "AggregateTransform",
    "AggregatedFieldDef",
    "Align",
    "AllSortString",
    "Angle",
    "AngleDatum",
    "AngleValue",
    "AnyMark",
    "AnyMarkConfig",
    "AreaConfig",
    "ArgmaxDef",
    "ArgminDef",
    "AutoSizeParams",
    "AutosizeType",
    "Axis",
    "AxisConfig",
    "AxisOrient",
    "AxisResolveMap",
    "BBox",
    "BarConfig",
    "BaseTitleNoValueRefs",
    "Baseline",
    "BinExtent",
    "BinParams",
    "BinTransform",
    "BindCheckbox",
    "BindDirect",
    "BindInput",
    "BindRadioSelect",
    "BindRange",
    "Binding",
    "BinnedTimeUnit",
    "Blend",
    "BoxPlot",
    "BoxPlotConfig",
    "BoxPlotDef",
    "BrushConfig",
    "CalculateTransform",
    "Categorical",
    "Color",
    "ColorDatum",
    "ColorDef",
    "ColorName",
    "ColorScheme",
    "ColorValue",
    "Column",
    "CompositeMark",
    "CompositeMarkDef",
    "CompositionConfig",
    "ConcatSpecGenericSpec",
    "ConditionalAxisColor",
    "ConditionalAxisLabelAlign",
    "ConditionalAxisLabelBaseline",
    "ConditionalAxisLabelFontStyle",
    "ConditionalAxisLabelFontWeight",
    "ConditionalAxisNumber",
    "ConditionalAxisNumberArray",
    "ConditionalAxisPropertyAlignnull",
    "ConditionalAxisPropertyColornull",
    "ConditionalAxisPropertyFontStylenull",
    "ConditionalAxisPropertyFontWeightnull",
    "ConditionalAxisPropertyTextBaselinenull",
    "ConditionalAxisPropertynumberArraynull",
    "ConditionalAxisPropertynumbernull",
    "ConditionalAxisPropertystringnull",
    "ConditionalAxisString",
    "ConditionalMarkPropFieldOrDatumDef",
    "ConditionalMarkPropFieldOrDatumDefTypeForShape",
    "ConditionalParameterMarkPropFieldOrDatumDef",
    "ConditionalParameterMarkPropFieldOrDatumDefTypeForShape",
    "ConditionalParameterStringFieldDef",
    "ConditionalParameterValueDefGradientstringnullExprRef",
    "ConditionalParameterValueDefTextExprRef",
    "ConditionalParameterValueDefnumber",
    "ConditionalParameterValueDefnumberArrayExprRef",
    "ConditionalParameterValueDefnumberExprRef",
    "ConditionalParameterValueDefstringExprRef",
    "ConditionalParameterValueDefstringnullExprRef",
    "ConditionalPredicateMarkPropFieldOrDatumDef",
    "ConditionalPredicateMarkPropFieldOrDatumDefTypeForShape",
    "ConditionalPredicateStringFieldDef",
    "ConditionalPredicateValueDefAlignnullExprRef",
    "ConditionalPredicateValueDefColornullExprRef",
    "ConditionalPredicateValueDefFontStylenullExprRef",
    "ConditionalPredicateValueDefFontWeightnullExprRef",
    "ConditionalPredicateValueDefGradientstringnullExprRef",
    "ConditionalPredicateValueDefTextBaselinenullExprRef",
    "ConditionalPredicateValueDefTextExprRef",
    "ConditionalPredicateValueDefnumber",
    "ConditionalPredicateValueDefnumberArrayExprRef",
    "ConditionalPredicateValueDefnumberArraynullExprRef",
    "ConditionalPredicateValueDefnumberExprRef",
    "ConditionalPredicateValueDefnumbernullExprRef",
    "ConditionalPredicateValueDefstringExprRef",
    "ConditionalPredicateValueDefstringnullExprRef",
    "ConditionalStringFieldDef",
    "ConditionalValueDefGradientstringnullExprRef",
    "ConditionalValueDefTextExprRef",
    "ConditionalValueDefnumber",
    "ConditionalValueDefnumberArrayExprRef",
    "ConditionalValueDefnumberExprRef",
    "ConditionalValueDefstringExprRef",
    "ConditionalValueDefstringnullExprRef",
    "Config",
    "CsvDataFormat",
    "Cursor",
    "Cyclical",
    "Data",
    "DataFormat",
    "DataSource",
    "Datasets",
    "DateTime",
    "DatumChannelMixin",
    "DatumDef",
    "Day",
    "DensityTransform",
    "DerivedStream",
    "Description",
    "DescriptionValue",
    "Detail",
    "DictInlineDataset",
    "DictSelectionInit",
    "DictSelectionInitInterval",
    "Diverging",
    "DomainUnionWith",
    "DsvDataFormat",
    "Element",
    "Encoding",
    "EncodingSortField",
    "ErrorBand",
    "ErrorBandConfig",
    "ErrorBandDef",
    "ErrorBar",
    "ErrorBarConfig",
    "ErrorBarDef",
    "ErrorBarExtent",
    "EventStream",
    "EventType",
    "Expr",
    "ExprRef",
    "ExtentTransform",
    "Facet",
    "FacetEncodingFieldDef",
    "FacetFieldDef",
    "FacetSpec",
    "FacetedEncoding",
    "FacetedUnitSpec",
    "Feature",
    "FeatureCollection",
    "FeatureGeometryGeoJsonProperties",
    "Field",
    "FieldChannelMixin",
    "FieldDefWithoutScale",
    "FieldEqualPredicate",
    "FieldGTEPredicate",
    "FieldGTPredicate",
    "FieldLTEPredicate",
    "FieldLTPredicate",
    "FieldName",
    "FieldOneOfPredicate",
    "FieldOrDatumDefWithConditionDatumDefGradientstringnull",
    "FieldOrDatumDefWithConditionDatumDefnumber",
    "FieldOrDatumDefWithConditionDatumDefnumberArray",
    "FieldOrDatumDefWithConditionDatumDefstringnull",
    "FieldOrDatumDefWithConditionMarkPropFieldDefGradientstringnull",
    "FieldOrDatumDefWithConditionMarkPropFieldDefTypeForShapestringnull",
    "FieldOrDatumDefWithConditionMarkPropFieldDefnumber",
    "FieldOrDatumDefWithConditionMarkPropFieldDefnumberArray",
    "FieldOrDatumDefWithConditionStringDatumDefText",
    "FieldOrDatumDefWithConditionStringFieldDefText",
    "FieldOrDatumDefWithConditionStringFieldDefstring",
    "FieldRange",
    "FieldRangePredicate",
    "FieldValidPredicate",
    "Fill",
    "FillDatum",
    "FillOpacity",
    "FillOpacityDatum",
    "FillOpacityValue",
    "FillValue",
    "FilterTransform",
    "Fit",
    "FlattenTransform",
    "FoldTransform",
    "FontStyle",
    "FontWeight",
    "FormatConfig",
    "Generator",
    "GenericUnitSpecEncodingAnyMark",
    "GeoJsonFeature",
    "GeoJsonFeatureCollection",
    "GeoJsonProperties",
    "Geometry",
    "GeometryCollection",
    "Gradient",
    "GradientStop",
    "GraticuleGenerator",
    "GraticuleParams",
    "HConcatSpecGenericSpec",
    "Header",
    "HeaderConfig",
    "HexColor",
    "Href",
    "HrefValue",
    "ImputeMethod",
    "ImputeParams",
    "ImputeSequence",
    "ImputeTransform",
    "InlineData",
    "InlineDataset",
    "Interpolate",
    "IntervalSelectionConfig",
    "IntervalSelectionConfigWithoutType",
    "JoinAggregateFieldDef",
    "JoinAggregateTransform",
    "JsonDataFormat",
    "Key",
    "LabelOverlap",
    "LatLongDef",
    "LatLongFieldDef",
    "Latitude",
    "Latitude2",
    "Latitude2Datum",
    "Latitude2Value",
    "LatitudeDatum",
    "LayerRepeatMapping",
    "LayerRepeatSpec",
    "LayerSpec",
    "LayoutAlign",
    "Legend",
    "LegendBinding",
    "LegendConfig",
    "LegendOrient",
    "LegendResolveMap",
    "LegendStreamBinding",
    "LineConfig",
    "LineString",
    "LinearGradient",
    "LocalMultiTimeUnit",
    "LocalSingleTimeUnit",
    "Locale",
    "LoessTransform",
    "LogicalAndPredicate",
    "LogicalNotPredicate",
    "LogicalOrPredicate",
    "Longitude",
    "Longitude2",
    "Longitude2Datum",
    "Longitude2Value",
    "LongitudeDatum",
    "LookupSelection",
    "LookupTransform",
    "Mark",
    "MarkConfig",
    "MarkDef",
    "MarkInvalidDataMode",
    "MarkPropDefGradientstringnull",
    "MarkPropDefnumber",
    "MarkPropDefnumberArray",
    "MarkPropDefstringnullTypeForShape",
    "MarkType",
    "MergedStream",
    "Month",
    "MultiLineString",
    "MultiPoint",
    "MultiPolygon",
    "MultiTimeUnit",
    "NamedData",
    "NonArgAggregateOp",
    "NonLayerRepeatSpec",
    "NonNormalizedSpec",
    "NumberLocale",
    "NumericArrayMarkPropDef",
    "NumericMarkPropDef",
    "OffsetDef",
    "Opacity",
    "OpacityDatum",
    "OpacityValue",
    "Order",
    "OrderFieldDef",
    "OrderOnlyDef",
    "OrderValue",
    "OrderValueDef",
    "Orient",
    "Orientation",
    "OverlayMarkDef",
    "Padding",
    "ParameterExtent",
    "ParameterName",
    "ParameterPredicate",
    "Parse",
    "ParseValue",
    "PivotTransform",
    "Point",
    "PointSelectionConfig",
    "PointSelectionConfigWithoutType",
    "PolarDef",
    "Polygon",
    "Position",
    "Position2Def",
    "PositionDatumDef",
    "PositionDatumDefBase",
    "PositionDef",
    "PositionFieldDef",
    "PositionFieldDefBase",
    "PositionValueDef",
    "Predicate",
    "PredicateComposition",
    "PrimitiveValue",
    "Projection",
    "ProjectionConfig",
    "ProjectionType",
    "QuantileTransform",
    "RadialGradient",
    "Radius",
    "Radius2",
    "Radius2Datum",
    "Radius2Value",
    "RadiusDatum",
    "RadiusValue",
    "RangeConfig",
    "RangeEnum",
    "RangeRaw",
    "RangeRawArray",
    "RangeScheme",
    "RectConfig",
    "RegressionTransform",
    "RelativeBandSize",
    "RepeatMapping",
    "RepeatRef",
    "RepeatSpec",
    "Resolve",
    "ResolveMode",
    "Root",
    "Row",
    "RowColLayoutAlign",
    "RowColboolean",
    "RowColnumber",
    "RowColumnEncodingFieldDef",
    "SampleTransform",
    "Scale",
    "ScaleBinParams",
    "ScaleBins",
    "ScaleConfig",
    "ScaleDatumDef",
    "ScaleFieldDef",
    "ScaleInterpolateEnum",
    "ScaleInterpolateParams",
    "ScaleInvalidDataConfig",
    "ScaleInvalidDataShowAsValueangle",
    "ScaleInvalidDataShowAsValuecolor",
    "ScaleInvalidDataShowAsValuefill",
    "ScaleInvalidDataShowAsValuefillOpacity",
    "ScaleInvalidDataShowAsValueopacity",
    "ScaleInvalidDataShowAsValueradius",
    "ScaleInvalidDataShowAsValueshape",
    "ScaleInvalidDataShowAsValuesize",
    "ScaleInvalidDataShowAsValuestroke",
    "ScaleInvalidDataShowAsValuestrokeDash",
    "ScaleInvalidDataShowAsValuestrokeOpacity",
    "ScaleInvalidDataShowAsValuestrokeWidth",
    "ScaleInvalidDataShowAsValuetheta",
    "ScaleInvalidDataShowAsValuex",
    "ScaleInvalidDataShowAsValuexOffset",
    "ScaleInvalidDataShowAsValuey",
    "ScaleInvalidDataShowAsValueyOffset",
    "ScaleInvalidDataShowAsangle",
    "ScaleInvalidDataShowAscolor",
    "ScaleInvalidDataShowAsfill",
    "ScaleInvalidDataShowAsfillOpacity",
    "ScaleInvalidDataShowAsopacity",
    "ScaleInvalidDataShowAsradius",
    "ScaleInvalidDataShowAsshape",
    "ScaleInvalidDataShowAssize",
    "ScaleInvalidDataShowAsstroke",
    "ScaleInvalidDataShowAsstrokeDash",
    "ScaleInvalidDataShowAsstrokeOpacity",
    "ScaleInvalidDataShowAsstrokeWidth",
    "ScaleInvalidDataShowAstheta",
    "ScaleInvalidDataShowAsx",
    "ScaleInvalidDataShowAsxOffset",
    "ScaleInvalidDataShowAsy",
    "ScaleInvalidDataShowAsyOffset",
    "ScaleResolveMap",
    "ScaleType",
    "SchemaBase",
    "SchemeParams",
    "SecondaryFieldDef",
    "SelectionConfig",
    "SelectionInit",
    "SelectionInitInterval",
    "SelectionInitIntervalMapping",
    "SelectionInitMapping",
    "SelectionParameter",
    "SelectionResolution",
    "SelectionType",
    "SequenceGenerator",
    "SequenceParams",
    "SequentialMultiHue",
    "SequentialSingleHue",
    "Shape",
    "ShapeDatum",
    "ShapeDef",
    "ShapeValue",
    "SharedEncoding",
    "SingleDefUnitChannel",
    "SingleTimeUnit",
    "Size",
    "SizeDatum",
    "SizeValue",
    "Sort",
    "SortArray",
    "SortByChannel",
    "SortByChannelDesc",
    "SortByEncoding",
    "SortField",
    "SortOrder",
    "Spec",
    "SphereGenerator",
    "StackOffset",
    "StackTransform",
    "StandardType",
    "Step",
    "StepFor",
    "Stream",
    "StringFieldDef",
    "StringFieldDefWithCondition",
    "StringValueDefWithCondition",
    "Stroke",
    "StrokeCap",
    "StrokeDash",
    "StrokeDashDatum",
    "StrokeDashValue",
    "StrokeDatum",
    "StrokeJoin",
    "StrokeOpacity",
    "StrokeOpacityDatum",
    "StrokeOpacityValue",
    "StrokeValue",
    "StrokeWidth",
    "StrokeWidthDatum",
    "StrokeWidthValue",
    "StyleConfigIndex",
    "SymbolShape",
    "Text",
    "TextBaseline",
    "TextDatum",
    "TextDef",
    "TextDirection",
    "TextValue",
    "Theta",
    "Theta2",
    "Theta2Datum",
    "Theta2Value",
    "ThetaDatum",
    "ThetaValue",
    "TickConfig",
    "TickCount",
    "TimeInterval",
    "TimeIntervalStep",
    "TimeLocale",
    "TimeUnit",
    "TimeUnitParams",
    "TimeUnitTransform",
    "TimeUnitTransformParams",
    "TitleAnchor",
    "TitleConfig",
    "TitleFrame",
    "TitleOrient",
    "TitleParams",
    "Tooltip",
    "TooltipContent",
    "TooltipValue",
    "TopLevelConcatSpec",
    "TopLevelFacetSpec",
    "TopLevelHConcatSpec",
    "TopLevelLayerSpec",
    "TopLevelParameter",
    "TopLevelRepeatSpec",
    "TopLevelSelectionParameter",
    "TopLevelSpec",
    "TopLevelUnitSpec",
    "TopLevelVConcatSpec",
    "TopoDataFormat",
    "Transform",
    "Type",
    "TypeForShape",
    "TypedFieldDef",
    "UnitSpec",
    "UnitSpecWithFrame",
    "Url",
    "UrlData",
    "UrlValue",
    "UtcMultiTimeUnit",
    "UtcSingleTimeUnit",
    "VConcatSpecGenericSpec",
    "ValueChannelMixin",
    "ValueDefWithConditionMarkPropFieldOrDatumDefGradientstringnull",
    "ValueDefWithConditionMarkPropFieldOrDatumDefTypeForShapestringnull",
    "ValueDefWithConditionMarkPropFieldOrDatumDefnumber",
    "ValueDefWithConditionMarkPropFieldOrDatumDefnumberArray",
    "ValueDefWithConditionMarkPropFieldOrDatumDefstringnull",
    "ValueDefWithConditionStringFieldDefText",
    "ValueDefnumber",
    "ValueDefnumberwidthheightExprRef",
    "VariableParameter",
    "Vector2DateTime",
    "Vector2Vector2number",
    "Vector2boolean",
    "Vector2number",
    "Vector2string",
    "Vector3number",
    "Vector7string",
    "Vector10string",
    "Vector12string",
    "VegaLiteSchema",
    "ViewBackground",
    "ViewConfig",
    "WindowEventType",
    "WindowFieldDef",
    "WindowOnlyOp",
    "WindowTransform",
    "X",
    "X2Datum",
    "X2Value",
    "XDatum",
    "XError",
    "XError2",
    "XError2Value",
    "XErrorValue",
    "XOffset",
    "XOffsetDatum",
    "XOffsetValue",
    "XValue",
    "Y",
    "Y2Datum",
    "Y2Value",
    "YDatum",
    "YError",
    "YError2",
    "YError2Value",
    "YErrorValue",
    "YOffset",
    "YOffsetDatum",
    "YOffsetValue",
    "YValue",
    "channels",
    "core",
    "load_schema",
    "with_property_setters",
]
