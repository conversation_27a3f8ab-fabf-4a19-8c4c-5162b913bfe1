# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/ButtonGroup.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import LabelVisibilityMessage_pb2 as streamlit_dot_proto_dot_LabelVisibilityMessage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!streamlit/proto/ButtonGroup.proto\x1a,streamlit/proto/LabelVisibilityMessage.proto\"\xf4\x05\n\x0b\x42uttonGroup\x12\n\n\x02id\x18\x01 \x01(\t\x12$\n\x07options\x18\x02 \x03(\x0b\x32\x13.ButtonGroup.Option\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x03 \x03(\r\x12\x10\n\x08\x64isabled\x18\x04 \x01(\x08\x12*\n\nclick_mode\x18\x05 \x01(\x0e\x32\x16.ButtonGroup.ClickMode\x12\x0f\n\x07\x66orm_id\x18\x06 \x01(\t\x12\r\n\x05value\x18\x07 \x03(\r\x12\x11\n\tset_value\x18\x08 \x01(\x08\x12\x44\n\x17selection_visualization\x18\t \x01(\x0e\x32#.ButtonGroup.SelectionVisualization\x12!\n\x05style\x18\n \x01(\x0e\x32\x12.ButtonGroup.Style\x12\r\n\x05label\x18\x0b \x01(\t\x12\x31\n\x10label_visibility\x18\x0c \x01(\x0b\x32\x17.LabelVisibilityMessage\x12\x11\n\x04help\x18\r \x01(\tH\x00\x88\x01\x01\x1a\xb7\x01\n\x06Option\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x1d\n\x10selected_content\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0c\x63ontent_icon\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\"\n\x15selected_content_icon\x18\x04 \x01(\tH\x02\x88\x01\x01\x42\x13\n\x11_selected_contentB\x0f\n\r_content_iconB\x18\n\x16_selected_content_icon\"0\n\tClickMode\x12\x11\n\rSINGLE_SELECT\x10\x00\x12\x10\n\x0cMULTI_SELECT\x10\x01\"C\n\x16SelectionVisualization\x12\x11\n\rONLY_SELECTED\x10\x00\x12\x16\n\x12\x41LL_UP_TO_SELECTED\x10\x01\"9\n\x05Style\x12\x15\n\x11SEGMENTED_CONTROL\x10\x00\x12\t\n\x05PILLS\x10\x01\x12\x0e\n\nBORDERLESS\x10\x02\x42\x07\n\x05_helpB0\n\x1c\x63om.snowflake.apps.streamlitB\x10\x42uttonGroupProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.ButtonGroup_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\020ButtonGroupProto'
  _globals['_BUTTONGROUP']._serialized_start=84
  _globals['_BUTTONGROUP']._serialized_end=840
  _globals['_BUTTONGROUP_OPTION']._serialized_start=470
  _globals['_BUTTONGROUP_OPTION']._serialized_end=653
  _globals['_BUTTONGROUP_CLICKMODE']._serialized_start=655
  _globals['_BUTTONGROUP_CLICKMODE']._serialized_end=703
  _globals['_BUTTONGROUP_SELECTIONVISUALIZATION']._serialized_start=705
  _globals['_BUTTONGROUP_SELECTIONVISUALIZATION']._serialized_end=772
  _globals['_BUTTONGROUP_STYLE']._serialized_start=774
  _globals['_BUTTONGROUP_STYLE']._serialized_end=831
# @@protoc_insertion_point(module_scope)
