"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ChatInput(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Position:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _PositionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ChatInput._Position.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        BOTTOM: ChatInput._Position.ValueType  # 0

    class Position(_Position, metaclass=_PositionEnumTypeWrapper):
        """DEPRECATED: position property is deprecated and unused."""

    BOTTOM: ChatInput.Position.ValueType  # 0

    class _AcceptFile:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _AcceptFileEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ChatInput._AcceptFile.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        NONE: ChatInput._AcceptFile.ValueType  # 0
        SINGLE: ChatInput._AcceptFile.ValueType  # 1
        MULTIPLE: ChatInput._AcceptFile.ValueType  # 2

    class AcceptFile(_AcceptFile, metaclass=_AcceptFileEnumTypeWrapper): ...
    NONE: ChatInput.AcceptFile.ValueType  # 0
    SINGLE: ChatInput.AcceptFile.ValueType  # 1
    MULTIPLE: ChatInput.AcceptFile.ValueType  # 2

    ID_FIELD_NUMBER: builtins.int
    PLACEHOLDER_FIELD_NUMBER: builtins.int
    MAX_CHARS_FIELD_NUMBER: builtins.int
    DISABLED_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    SET_VALUE_FIELD_NUMBER: builtins.int
    DEFAULT_FIELD_NUMBER: builtins.int
    POSITION_FIELD_NUMBER: builtins.int
    ACCEPT_FILE_FIELD_NUMBER: builtins.int
    FILE_TYPE_FIELD_NUMBER: builtins.int
    MAX_UPLOAD_SIZE_MB_FIELD_NUMBER: builtins.int
    id: builtins.str
    placeholder: builtins.str
    max_chars: builtins.int
    disabled: builtins.bool
    value: builtins.str
    set_value: builtins.bool
    default: builtins.str
    position: global___ChatInput.Position.ValueType
    accept_file: global___ChatInput.AcceptFile.ValueType
    max_upload_size_mb: builtins.int
    """Max file size allowed by server config"""
    @property
    def file_type(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Supported file types: For example: ["png","jpg","img"]"""

    def __init__(
        self,
        *,
        id: builtins.str = ...,
        placeholder: builtins.str = ...,
        max_chars: builtins.int = ...,
        disabled: builtins.bool = ...,
        value: builtins.str = ...,
        set_value: builtins.bool = ...,
        default: builtins.str = ...,
        position: global___ChatInput.Position.ValueType = ...,
        accept_file: global___ChatInput.AcceptFile.ValueType = ...,
        file_type: collections.abc.Iterable[builtins.str] | None = ...,
        max_upload_size_mb: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["accept_file", b"accept_file", "default", b"default", "disabled", b"disabled", "file_type", b"file_type", "id", b"id", "max_chars", b"max_chars", "max_upload_size_mb", b"max_upload_size_mb", "placeholder", b"placeholder", "position", b"position", "set_value", b"set_value", "value", b"value"]) -> None: ...

global___ChatInput = ChatInput
