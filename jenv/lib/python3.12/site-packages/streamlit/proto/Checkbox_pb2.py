# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Checkbox.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import LabelVisibilityMessage_pb2 as streamlit_dot_proto_dot_LabelVisibilityMessage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1estreamlit/proto/Checkbox.proto\x1a,streamlit/proto/LabelVisibilityMessage.proto\"\x85\x02\n\x08\x43heckbox\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x03 \x01(\x08\x12\x0c\n\x04help\x18\x04 \x01(\t\x12\x0f\n\x07\x66orm_id\x18\x05 \x01(\t\x12\r\n\x05value\x18\x06 \x01(\x08\x12\x11\n\tset_value\x18\x07 \x01(\x08\x12\x10\n\x08\x64isabled\x18\x08 \x01(\x08\x12\x31\n\x10label_visibility\x18\t \x01(\x0b\x32\x17.LabelVisibilityMessage\x12!\n\x04type\x18\n \x01(\x0e\x32\x13.Checkbox.StyleType\"$\n\tStyleType\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\n\n\x06TOGGLE\x10\x01\x42-\n\x1c\x63om.snowflake.apps.streamlitB\rCheckboxProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Checkbox_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\rCheckboxProto'
  _globals['_CHECKBOX']._serialized_start=81
  _globals['_CHECKBOX']._serialized_end=342
  _globals['_CHECKBOX_STYLETYPE']._serialized_start=306
  _globals['_CHECKBOX_STYLETYPE']._serialized_end=342
# @@protoc_insertion_point(module_scope)
