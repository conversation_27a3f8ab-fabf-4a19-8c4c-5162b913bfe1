"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _GapSize:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _GapSizeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_GapSize.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    GAP_UNDEFINED: _GapSize.ValueType  # 0
    SMALL: _GapSize.ValueType  # 1
    MEDIUM: _GapSize.ValueType  # 2
    LARGE: _GapSize.ValueType  # 3
    NONE: _GapSize.ValueType  # 4

class GapSize(_GapSize, metaclass=_GapSizeEnumTypeWrapper): ...

GAP_UNDEFINED: GapSize.ValueType  # 0
SMALL: GapSize.ValueType  # 1
MEDIUM: GapSize.ValueType  # 2
LARGE: GapSize.ValueType  # 3
NONE: GapSize.ValueType  # 4
global___GapSize = GapSize

@typing.final
class GapConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GAP_SIZE_FIELD_NUMBER: builtins.int
    gap_size: global___GapSize.ValueType
    def __init__(
        self,
        *,
        gap_size: global___GapSize.ValueType = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["gap_size", b"gap_size", "gap_spec", b"gap_spec"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["gap_size", b"gap_size", "gap_spec", b"gap_spec"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["gap_spec", b"gap_spec"]) -> typing.Literal["gap_size"] | None: ...

global___GapConfig = GapConfig
