# Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

from streamlit import runtime
from streamlit.deprecation_util import show_deprecation_warning
from streamlit.runtime.metrics_util import gather_metrics
from streamlit.runtime.scriptrunner_utils.script_run_context import get_script_run_ctx
from streamlit.web.server.browser_websocket_handler import BrowserWebSocketHandler

_GET_WEBSOCKET_HEADERS_DEPRECATE_MSG = (
    "The `_get_websocket_headers` function is deprecated and will be removed "
    "in a future version of Streamlit. Please use `st.context.headers` instead."
)


@gather_metrics("_get_websocket_headers")
def _get_websocket_headers() -> dict[str, str] | None:
    """Return a copy of the HTTP request headers for the current session's
    WebSocket connection. If there's no active session, return None instead.

    Raise an error if the server is not running.

    Note to the intrepid: this is an UNSUPPORTED, INTERNAL API. (We don't have plans
    to remove it without a replacement, but we don't consider this a production-ready
    function, and its signature may change without a deprecation warning.)
    """

    show_deprecation_warning(_GET_WEBSOCKET_HEADERS_DEPRECATE_MSG)

    ctx = get_script_run_ctx()
    if ctx is None:
        return None

    session_client = runtime.get_instance().get_client(ctx.session_id)
    if session_client is None:
        return None

    if not isinstance(session_client, BrowserWebSocketHandler):
        raise TypeError(
            f"SessionClient is not a BrowserWebSocketHandler! ({session_client})"
        )

    return dict(session_client.request.headers)
