#!/usr/bin/env python3
"""
Quick run script for Job Scraping Agentic System
"""

import os
import sys
import subprocess
from datetime import datetime

def check_setup():
    """Check if system is properly set up"""
    if not os.path.exists(".env"):
        print("❌ .env file not found. Running setup...")
        subprocess.run([sys.executable, "setup.py"])
        return False
    
    if not os.path.exists("config/agents.yaml"):
        print("❌ Configuration files missing. Please check your setup.")
        return False
    
    return True

def run_tests():
    """Run system tests"""
    print("🧪 Running system tests...")
    result = subprocess.run([sys.executable, "test_system.py"], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed:")
        print(result.stdout)
        print(result.stderr)
        return False

def main():
    """Main execution function"""
    print("🚀 Job Scraping Agentic System - Quick Start")
    print("=" * 50)
    
    # Check setup
    if not check_setup():
        print("Please complete setup before running the system.")
        return
    
    # Ask user if they want to run tests
    run_test = input("Run system tests first? (y/n, default: y): ").strip().lower()
    if run_test != 'n':
        if not run_tests():
            proceed = input("Tests failed. Continue anyway? (y/n, default: n): ").strip().lower()
            if proceed != 'y':
                print("Exiting. Please fix the issues and try again.")
                return
    
    print("\n🎯 Starting Job Scraping System...")
    
    # Run the main system
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n⏹️  Process interrupted by user.")
    except Exception as e:
        print(f"\n❌ Error running system: {e}")

if __name__ == "__main__":
    main()
