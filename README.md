# 🤖 Job Scraping Agentic System

A comprehensive AI-powered job opportunity scraping and analysis system built with CrewAI and Google Gemini. This system automatically searches multiple job platforms, extracts detailed job information, filters opportunities based on experience requirements (1+ years), and generates actionable career insights.

## 🌟 Features

### 🔍 Multi-Platform Job Scraping
- **Indeed**: Comprehensive job board scraping
- **LinkedIn**: Professional network job postings
- **Glassdoor**: Company reviews and job listings
- **Extensible**: Easy to add more platforms

### 🤖 AI-Powered Agents
- **Job Search Agent**: Discovers opportunities across platforms
- **Data Extraction Agent**: Extracts and structures job data
- **Filter Agent**: Applies experience and quality filters
- **Analysis Agent**: Evaluates and ranks opportunities
- **Report Agent**: Generates comprehensive career reports

### 🎯 Smart Filtering
- Experience level targeting (1+ years focus)
- Quality scoring based on multiple factors
- Duplicate removal and spam filtering
- Category-based job organization

### 📊 Comprehensive Analysis
- Market trend analysis
- Salary range insights
- Skills gap identification
- Company reputation scoring
- Application strategy recommendations

## 🚀 Quick Start

### 1. Setup
```bash
# Clone or download the system files
# Navigate to the project directory

# Run the setup script
python setup.py
```

### 2. Configuration
Edit the `.env` file with your API keys:
```env
GOOGLE_API_KEY=your_gemini_api_key_here
```

### 3. Run the System
```bash
python main.py
```

Follow the interactive prompts to configure your job search parameters.

## 📁 Project Structure

```
job-scraping-system/
├── main.py                 # Main execution file
├── crew.py                 # CrewAI crew configuration
├── setup.py               # Setup and installation script
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables (API keys)
├── README.md             # This file
├── config/
│   ├── agents.yaml       # Agent configurations
│   └── tasks.yaml        # Task definitions
├── tools/
│   └── job_scraper_tools.py  # Custom scraping tools
└── output/
    └── career_opportunities_report.md  # Generated reports
```

## 🛠️ System Architecture

### Agent Flow
1. **Job Search Agent** → Searches multiple platforms for opportunities
2. **Data Extraction Agent** → Extracts detailed job information
3. **Filter Agent** → Applies experience and quality filters
4. **Analysis Agent** → Analyzes market trends and opportunities
5. **Report Agent** → Generates comprehensive career report

### Technology Stack
- **Framework**: CrewAI for agent orchestration
- **LLM**: Google Gemini 1.5 Pro for intelligent processing
- **Scraping**: BeautifulSoup, Requests, Selenium
- **Data Processing**: Pandas, JSON
- **Environment**: Python 3.8+

## 🎯 Target Use Cases

### For Job Seekers (1+ Years Experience)
- Automated job discovery across multiple platforms
- Quality-filtered opportunities matching experience level
- Market insights and salary trends
- Skills gap analysis and development recommendations
- Strategic application guidance

### For Career Coaches
- Market analysis for client guidance
- Industry trend identification
- Skill demand assessment
- Salary benchmarking data

### For Recruiters
- Competitive landscape analysis
- Market rate insights
- Skill demand trends
- Platform effectiveness analysis

## 📊 Output and Reports

The system generates a comprehensive markdown report including:

### Executive Summary
- Key market findings
- Top opportunity highlights
- Strategic recommendations

### Detailed Analysis
- Filtered job opportunities with quality scores
- Company profiles and ratings
- Salary analysis and trends
- Skills requirements breakdown

### Strategic Recommendations
- Priority applications (top-ranked opportunities)
- Skill development suggestions
- Networking strategies
- Application timeline and approach

### Data Appendices
- Complete job listings with metadata
- Company research summaries
- Industry trend analysis
- Resource links and contacts

## ⚙️ Configuration Options

### Search Parameters
- **Search Terms**: Customize job search keywords
- **Experience Level**: Target specific experience ranges
- **Location**: Remote, specific cities, or regions
- **Job Types**: Full-time, contract, part-time
- **Industries**: Tech, finance, healthcare, etc.

### Filtering Criteria
- Experience requirements (1+ years focus)
- Salary ranges
- Company size preferences
- Remote work options
- Benefits and perks

### Analysis Settings
- Quality scoring weights
- Category definitions
- Ranking algorithms
- Report formatting options

## 🔧 Customization

### Adding New Job Platforms
1. Create a new search method in `JobSearchTool`
2. Add platform-specific extraction logic
3. Update the platform list in the search configuration

### Modifying Filter Criteria
1. Edit the `JobFilterTool` class methods
2. Adjust experience level patterns
3. Update quality scoring algorithms

### Customizing Reports
1. Modify the report agent configuration
2. Update report templates in `tasks.yaml`
3. Adjust output formatting preferences

## 🚨 Important Notes

### Rate Limiting
- Built-in delays between requests to respect platform policies
- Configurable rate limiting for different platforms
- Error handling for blocked requests

### Legal Compliance
- Respects robots.txt files
- Uses appropriate user agents
- Implements reasonable request delays
- For educational and personal use

### Data Privacy
- No personal data storage
- Temporary processing only
- Local report generation
- No data transmission to third parties

## 🐛 Troubleshooting

### Common Issues

**API Key Errors**
- Ensure your Gemini API key is correctly set in `.env`
- Check API key permissions and quotas

**Scraping Failures**
- Some platforms have anti-bot measures
- Try adjusting request delays
- Check internet connectivity

**Missing Dependencies**
- Run `python setup.py` to install requirements
- Ensure Python 3.8+ is installed

**Chrome Driver Issues**
- WebDriver Manager handles Chrome driver automatically
- Ensure Chrome browser is installed

### Getting Help
1. Check the console output for detailed error messages
2. Verify your `.env` configuration
3. Ensure all dependencies are installed
4. Check platform-specific rate limits

## 🔮 Future Enhancements

- Integration with more job platforms
- Real-time job alerts and notifications
- Advanced ML-based job matching
- Integration with applicant tracking systems
- Mobile app development
- API for third-party integrations

## 📄 License

This project is for educational and personal use. Please respect the terms of service of the job platforms being scraped.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

---

**Happy Job Hunting! 🎯**
