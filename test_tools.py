#!/usr/bin/env python3
"""
Simple test script to verify the job search tools work
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_job_tools():
    """Test the job search and filter tools"""
    print("🧪 Testing Job Search Tools...")
    
    try:
        # Import tools
        from tools.job_scraper_tools import DataScienceJobSearchTool, DataScienceJobFilterTool
        print("✅ Data science tools imported successfully")

        # Initialize tools
        search_tool = DataScienceJobSearchTool()
        filter_tool = DataScienceJobFilterTool()
        print("✅ Tools initialized successfully")

        # Test job search with real web search
        print("\n🔍 Testing data science job search with Tavily API...")
        jobs = search_tool._run(
            search_terms="data scientist python",
            location="Bangalore",
            experience_years="1",
            max_results=5
        )
        print(f"✅ Found {len(jobs)} data science jobs")

        # Test job filtering
        print("\n🎯 Testing data science job filtering...")
        filtered_results = filter_tool._run(
            jobs_data=jobs,
            location="Bangalore",
            experience_years="1"
        )
        filtered_jobs = filtered_results.get("filtered_jobs", [])
        print(f"✅ Filtered to {len(filtered_jobs)} jobs")

        # Display sample results
        print("\n📋 Sample Data Science Job Results:")
        for i, job in enumerate(filtered_jobs[:3], 1):
            print(f"{i}. {job.get('title')} at {job.get('company')}")
            print(f"   Location: {job.get('location')}")
            print(f"   Experience: {job.get('experience_required')}")
            print(f"   Skills: {', '.join(job.get('skills', [])[:5])}")
            print(f"   Quality Score: {job.get('quality_score', 0)}")
            print(f"   Platform: {job.get('platform')}")
            print(f"   URL: {job.get('url')}")
            print()

        print("🎉 All tests passed! Data science tools are working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_imports():
    """Test if Streamlit can be imported"""
    print("\n🧪 Testing Streamlit imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
        
        import pandas as pd
        print("✅ Pandas imported successfully")
        
        print("✅ All required packages are available")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("💡 Run: pip install streamlit pandas")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🔍 JOB SCRAPER - TESTING SUITE")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_streamlit_imports()
    
    # Test tools
    tools_ok = test_job_tools()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if imports_ok and tools_ok:
        print("🎉 All tests passed! System is ready to use.")
        print("\n🚀 Next steps:")
        print("1. Run: streamlit run app.py")
        print("2. Open browser to http://localhost:8501")
        print("3. Search for jobs in Bangalore with 1 year experience")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        if not imports_ok:
            print("💡 Install missing packages: pip install streamlit pandas")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
