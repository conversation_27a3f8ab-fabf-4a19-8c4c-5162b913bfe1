import requests
import time
import json
import re
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import Chrome<PERSON>river<PERSON>anager
from fake_useragent import UserAgent
from typing import List, Dict, Any
from crewai_tools import BaseTool
from pydantic import BaseModel, Field


class JobSearchTool(BaseTool):
    name: str = "job_search_tool"
    description: str = "Search for job opportunities across multiple job platforms"

    def _run(self, search_terms: str, experience_level: str = "1-3 years",
             location: str = "remote", max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for jobs across multiple platforms"""
        job_results = []

        # Job platforms to search
        platforms = {
            "indeed": self._search_indeed,
            "glassdoor": self._search_glassdoor,
            "linkedin": self._search_linkedin_jobs,
        }

        for platform_name, search_func in platforms.items():
            try:
                print(f"Searching {platform_name}...")
                platform_results = search_func(search_terms, experience_level, location)
                job_results.extend(platform_results)
                time.sleep(2)  # Rate limiting
            except Exception as e:
                print(f"Error searching {platform_name}: {str(e)}")
                continue

        return job_results[:max_results]

    def _get_driver(self):
        """Setup Chrome driver with options"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        ua = UserAgent()
        chrome_options.add_argument(f"--user-agent={ua.random}")

        driver = webdriver.Chrome(options=chrome_options)
        return driver

    def _search_indeed(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search Indeed for jobs"""
        jobs = []

        try:
            headers = {'User-Agent': UserAgent().random}
            base_url = "https://www.indeed.com/jobs"
            params = {
                "q": f"{search_terms} {experience_level}",
                "l": location,
                "fromage": "7",
                "sort": "date"
            }

            url = f"{base_url}?" + "&".join([f"{k}={v.replace(' ', '+')}" for k, v in params.items()])
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                job_cards = soup.find_all('div', class_='job_seen_beacon')

                for card in job_cards[:20]:
                    try:
                        title_elem = card.find('h2', class_='jobTitle')
                        company_elem = card.find('span', class_='companyName')
                        location_elem = card.find('div', class_='companyLocation')
                        link_elem = card.find('h2', class_='jobTitle').find('a') if title_elem else None

                        if title_elem and company_elem and link_elem:
                            job_data = {
                                "title": title_elem.get_text().strip(),
                                "company": company_elem.get_text().strip(),
                                "location": location_elem.get_text().strip() if location_elem else location,
                                "url": "https://www.indeed.com" + link_elem.get('href'),
                                "platform": "Indeed",
                                "posted_date": "Recent"
                            }
                            jobs.append(job_data)
                    except Exception as e:
                        continue

        except Exception as e:
            print(f"Error searching Indeed: {str(e)}")

        return jobs

    def _search_glassdoor(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search Glassdoor for jobs"""
        jobs = []

        try:
            headers = {'User-Agent': UserAgent().random}
            base_url = "https://www.glassdoor.com/Job/jobs.htm"
            params = {
                "sc.keyword": search_terms,
                "locT": "C",
                "locId": "1147401",  # Default to SF
                "jobType": "fulltime"
            }

            url = f"{base_url}?" + "&".join([f"{k}={v.replace(' ', '+')}" for k, v in params.items()])
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                job_cards = soup.find_all('li', class_='react-job-listing')

                for card in job_cards[:15]:
                    try:
                        title_elem = card.find('a', {'data-test': 'job-title'})
                        company_elem = card.find('span', class_='employer-name')
                        location_elem = card.find('span', class_='job-location')

                        if title_elem and company_elem:
                            job_data = {
                                "title": title_elem.get_text().strip(),
                                "company": company_elem.get_text().strip(),
                                "location": location_elem.get_text().strip() if location_elem else location,
                                "url": "https://www.glassdoor.com" + title_elem.get('href'),
                                "platform": "Glassdoor",
                                "posted_date": "Recent"
                            }
                            jobs.append(job_data)
                    except Exception as e:
                        continue

        except Exception as e:
            print(f"Error searching Glassdoor: {str(e)}")

        return jobs

    def _search_linkedin_jobs(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search LinkedIn Jobs"""
        jobs = []

        try:
            headers = {'User-Agent': UserAgent().random}
            base_url = "https://www.linkedin.com/jobs-guest/jobs/api/seeMoreJobPostings/search"
            params = {
                "keywords": search_terms,
                "location": location,
                "f_TPR": "r604800",  # Past week
                "f_E": "2",  # 1-2 years experience
                "start": "0"
            }

            url = f"{base_url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                job_cards = soup.find_all('div', class_='base-card')

                for card in job_cards[:15]:
                    try:
                        title_elem = card.find('h3', class_='base-search-card__title')
                        company_elem = card.find('h4', class_='base-search-card__subtitle')
                        location_elem = card.find('span', class_='job-search-card__location')
                        link_elem = card.find('a', class_='base-card__full-link')

                        if title_elem and company_elem and link_elem:
                            job_data = {
                                "title": title_elem.get_text().strip(),
                                "company": company_elem.get_text().strip(),
                                "location": location_elem.get_text().strip() if location_elem else location,
                                "url": link_elem.get('href'),
                                "platform": "LinkedIn",
                                "posted_date": "Recent"
                            }
                            jobs.append(job_data)
                    except Exception as e:
                        continue

        except Exception as e:
            print(f"Error searching LinkedIn: {str(e)}")

        return jobs


class JobDataExtractorTool(BaseTool):
    name: str = "job_data_extractor"
    description: str = "Extract detailed information from job posting URLs"

    def _run(self, job_data_list: List[Dict]) -> List[Dict[str, Any]]:
        """Extract detailed job information from job data"""
        extracted_jobs = []

        for job_data in job_data_list:
            try:
                enhanced_job = self._extract_job_details(job_data)
                if enhanced_job:
                    extracted_jobs.append(enhanced_job)
                time.sleep(1)  # Rate limiting
            except Exception as e:
                print(f"Error extracting from {job_data.get('url', 'unknown')}: {str(e)}")
                continue

        return extracted_jobs

    def _extract_job_details(self, job_data: Dict) -> Dict[str, Any]:
        """Extract details from a single job posting"""
        headers = {'User-Agent': UserAgent().random}

        try:
            response = requests.get(job_data['url'], headers=headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Enhance the existing job data
            enhanced_data = job_data.copy()
            enhanced_data.update({
                "description": self._extract_description(soup),
                "requirements": self._extract_requirements(soup),
                "salary": self._extract_salary(soup),
                "job_type": self._extract_job_type(soup),
                "experience_required": self._extract_experience(soup),
                "skills": self._extract_skills(soup),
                "benefits": self._extract_benefits(soup)
            })

            return enhanced_data

        except Exception as e:
            print(f"Error extracting job details: {str(e)}")
            return job_data  # Return original data if extraction fails

    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract job description"""
        selectors = [
            '#jobDescriptionText',
            '.jobsearch-jobDescriptionText',
            '.description',
            '[data-testid="jobDescription"]',
            '.job-description'
        ]

        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text().strip()[:2000]  # Limit length
        return "Description not available"

    def _extract_requirements(self, soup: BeautifulSoup) -> List[str]:
        """Extract job requirements"""
        description = self._extract_description(soup)
        requirements = []

        # Look for common requirement patterns
        req_patterns = [
            r'(?:requirements?|qualifications?|must have)[:\s]*([^.]+)',
            r'(?:experience with|knowledge of|proficient in)[:\s]*([^.]+)',
            r'(?:\d+\+?\s*years?)[^.]*([^.]+)'
        ]

        for pattern in req_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            requirements.extend([match.strip() for match in matches if match.strip()])

        return list(set(requirements))[:10]  # Remove duplicates and limit

    def _extract_salary(self, soup: BeautifulSoup) -> str:
        """Extract salary information"""
        salary_patterns = [
            r'\$[\d,]+(?:\s*-\s*\$[\d,]+)?(?:\s*(?:per|/)\s*(?:year|hour|month))?',
            r'[\d,]+k?(?:\s*-\s*[\d,]+k?)?\s*(?:per|/)\s*(?:year|hour|month)'
        ]

        text = soup.get_text()
        for pattern in salary_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group()

        return "Not specified"

    def _extract_job_type(self, soup: BeautifulSoup) -> str:
        """Extract job type"""
        text = soup.get_text().lower()
        if 'full-time' in text or 'full time' in text:
            return 'Full-time'
        elif 'part-time' in text or 'part time' in text:
            return 'Part-time'
        elif 'contract' in text:
            return 'Contract'
        elif 'internship' in text:
            return 'Internship'
        return 'Not specified'

    def _extract_experience(self, soup: BeautifulSoup) -> str:
        """Extract experience requirements"""
        text = soup.get_text()
        exp_patterns = [
            r'(\d+\+?\s*(?:to\s+\d+\s*)?years?\s*(?:of\s*)?experience)',
            r'(entry.level|junior|senior|mid.level)',
            r'(\d+\+?\s*years?)'
        ]

        for pattern in exp_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return "Not specified"

    def _extract_skills(self, soup: BeautifulSoup) -> List[str]:
        """Extract required skills"""
        text = soup.get_text().lower()

        # Common tech skills to look for
        skills_keywords = [
            'python', 'javascript', 'java', 'react', 'node.js', 'sql', 'aws',
            'docker', 'kubernetes', 'git', 'agile', 'scrum', 'html', 'css',
            'machine learning', 'data science', 'tensorflow', 'pytorch',
            'angular', 'vue.js', 'mongodb', 'postgresql', 'redis', 'elasticsearch',
            'c++', 'c#', '.net', 'spring', 'django', 'flask', 'ruby', 'php',
            'go', 'rust', 'scala', 'kotlin', 'swift', 'typescript'
        ]

        found_skills = []
        for skill in skills_keywords:
            if skill in text:
                found_skills.append(skill.title())

        return list(set(found_skills))[:15]  # Remove duplicates and limit

    def _extract_benefits(self, soup: BeautifulSoup) -> List[str]:
        """Extract benefits information"""
        text = soup.get_text().lower()
        benefits_keywords = [
            'health insurance', 'dental', 'vision', '401k', 'retirement',
            'remote work', 'flexible hours', 'pto', 'vacation', 'sick leave',
            'stock options', 'equity', 'bonus', 'gym', 'wellness'
        ]

        found_benefits = []
        for benefit in benefits_keywords:
            if benefit in text:
                found_benefits.append(benefit.title())

        return list(set(found_benefits))[:10]


class JobFilterTool(BaseTool):
    name: str = "job_filter_tool"
    description: str = "Filter and categorize job opportunities based on specific criteria"

    def _run(self, jobs_data: List[Dict]) -> Dict[str, Any]:
        """Filter and categorize jobs"""
        filtered_results = {
            "filtered_jobs": [],
            "statistics": {},
            "categories": {},
            "quality_score": {}
        }

        # Filter jobs based on experience requirements
        experience_filtered = self._filter_by_experience(jobs_data)

        # Remove duplicates
        deduplicated = self._remove_duplicates(experience_filtered)

        # Quality filtering
        quality_filtered = self._quality_filter(deduplicated)

        # Categorize jobs
        categorized = self._categorize_jobs(quality_filtered)

        # Score jobs
        scored_jobs = self._score_jobs(quality_filtered)

        filtered_results["filtered_jobs"] = scored_jobs
        filtered_results["statistics"] = self._generate_statistics(jobs_data, scored_jobs)
        filtered_results["categories"] = categorized

        return filtered_results

    def _filter_by_experience(self, jobs: List[Dict]) -> List[Dict]:
        """Filter jobs by experience requirements"""
        filtered = []

        for job in jobs:
            experience = job.get('experience_required', '').lower()
            description = job.get('description', '').lower()

            # Look for 1+ years experience indicators
            experience_indicators = [
                '1+ year', '1-2 year', '1-3 year', '1 to 2 year', '1 to 3 year',
                'junior', 'entry level with experience', 'some experience'
            ]

            # Exclude senior positions
            senior_indicators = [
                'senior', '5+ year', '3+ year', 'lead', 'principal', 'architect'
            ]

            has_suitable_experience = any(indicator in experience or indicator in description
                                        for indicator in experience_indicators)
            is_too_senior = any(indicator in experience or indicator in description
                              for indicator in senior_indicators)

            if has_suitable_experience and not is_too_senior:
                filtered.append(job)
            elif not experience or experience == 'not specified':
                # Include jobs with unspecified experience
                filtered.append(job)

        return filtered

    def _remove_duplicates(self, jobs: List[Dict]) -> List[Dict]:
        """Remove duplicate job postings"""
        seen = set()
        unique_jobs = []

        for job in jobs:
            # Create a unique identifier
            identifier = f"{job.get('title', '').lower()}_{job.get('company', '').lower()}"

            if identifier not in seen:
                seen.add(identifier)
                unique_jobs.append(job)

        return unique_jobs

    def _quality_filter(self, jobs: List[Dict]) -> List[Dict]:
        """Filter out low-quality job postings"""
        quality_jobs = []

        for job in jobs:
            # Quality checks
            has_description = len(job.get('description', '')) > 100
            has_company = job.get('company', '') != 'N/A' and job.get('company', '') != ''
            has_title = job.get('title', '') != 'N/A' and job.get('title', '') != ''

            # Check for spam indicators
            spam_indicators = ['make money fast', 'work from home easy', 'no experience needed']
            is_spam = any(indicator in job.get('description', '').lower()
                         for indicator in spam_indicators)

            if has_description and has_company and has_title and not is_spam:
                quality_jobs.append(job)

        return quality_jobs

    def _categorize_jobs(self, jobs: List[Dict]) -> Dict[str, List[Dict]]:
        """Categorize jobs by field/industry"""
        categories = {
            "Software Development": [],
            "Data Science": [],
            "DevOps/Cloud": [],
            "Product Management": [],
            "Design": [],
            "Marketing": [],
            "Other": []
        }

        for job in jobs:
            title = job.get('title', '').lower()
            description = job.get('description', '').lower()

            if any(keyword in title or keyword in description
                   for keyword in ['developer', 'engineer', 'programmer', 'software']):
                categories["Software Development"].append(job)
            elif any(keyword in title or keyword in description
                     for keyword in ['data scientist', 'analyst', 'machine learning', 'ai']):
                categories["Data Science"].append(job)
            elif any(keyword in title or keyword in description
                     for keyword in ['devops', 'cloud', 'infrastructure', 'sre']):
                categories["DevOps/Cloud"].append(job)
            elif any(keyword in title or keyword in description
                     for keyword in ['product manager', 'product owner', 'pm']):
                categories["Product Management"].append(job)
            elif any(keyword in title or keyword in description
                     for keyword in ['designer', 'ux', 'ui', 'design']):
                categories["Design"].append(job)
            elif any(keyword in title or keyword in description
                     for keyword in ['marketing', 'growth', 'digital marketing']):
                categories["Marketing"].append(job)
            else:
                categories["Other"].append(job)

        return categories

    def _score_jobs(self, jobs: List[Dict]) -> List[Dict]:
        """Score jobs based on various factors"""
        for job in jobs:
            score = 0

            # Salary information available
            if job.get('salary', 'Not specified') != 'Not specified':
                score += 20

            # Remote work option
            if 'remote' in job.get('location', '').lower():
                score += 15

            # Description quality
            description_length = len(job.get('description', ''))
            if description_length > 500:
                score += 15
            elif description_length > 200:
                score += 10

            # Skills match (more skills = higher score)
            skills_count = len(job.get('skills', []))
            score += min(skills_count * 2, 20)

            # Benefits available
            benefits_count = len(job.get('benefits', []))
            score += min(benefits_count * 3, 15)

            # Platform reputation
            platform_scores = {
                'LinkedIn': 15,
                'Indeed': 10,
                'Glassdoor': 12,
                'Stack Overflow': 15
            }
            score += platform_scores.get(job.get('platform', ''), 5)

            job['quality_score'] = min(score, 100)  # Cap at 100

        # Sort by score
        return sorted(jobs, key=lambda x: x.get('quality_score', 0), reverse=True)

    def _generate_statistics(self, original_jobs: List[Dict], filtered_jobs: List[Dict]) -> Dict:
        """Generate filtering statistics"""
        return {
            "total_jobs_found": len(original_jobs),
            "jobs_after_filtering": len(filtered_jobs),
            "filter_rate": f"{(1 - len(filtered_jobs)/len(original_jobs))*100:.1f}%" if original_jobs else "0%",
            "platforms_searched": list(set(job.get('platform', '') for job in original_jobs)),
            "average_quality_score": sum(job.get('quality_score', 0) for job in filtered_jobs) / len(filtered_jobs) if filtered_jobs else 0
        }
