import os
import re
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# CrewAI tools imports
from crewai.tools import BaseTool
from crewai_tools import ScrapeWebsiteTool
from tavily import TavilyClient

# Load environment variables
load_dotenv()


class JobListing(BaseModel):
    """Pydantic model for job listing data"""
    title: str = Field(..., description="Job title")
    company: str = Field(..., description="Company name")
    location: str = Field(..., description="Job location")
    experience_required: str = Field(..., description="Required experience")
    url: str = Field(..., description="Job posting URL")
    platform: str = Field(..., description="Platform where job was found")
    description: str = Field(..., description="Job description")
    skills: List[str] = Field(default=[], description="Required skills")
    salary: Optional[str] = Field(None, description="Salary information")
    posted_date: Optional[str] = Field(None, description="When job was posted")
    job_type: Optional[str] = Field(None, description="Job type (full-time, contract, etc.)")
    quality_score: int = Field(default=0, description="Quality score 0-100")


class DataScienceJobSearchTool(BaseTool):
    """Real web search tool for data science jobs using Tavily API"""

    name: str = "data_science_job_search_tool"
    description: str = "Search for data science job opportunities using web search"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Initialize clients after parent initialization
        self._tavily_client = None
        self._scrape_tool = None

    @property
    def tavily_client(self):
        if self._tavily_client is None:
            self._tavily_client = TavilyClient(api_key=os.getenv("TAVILY_API_KEY"))
        return self._tavily_client

    @property
    def scrape_tool(self):
        if self._scrape_tool is None:
            self._scrape_tool = ScrapeWebsiteTool()
        return self._scrape_tool

    def _run(self, search_terms: str = "data scientist", location: str = "Bangalore",
             experience_years: str = "1", max_results: int = 20) -> List[Dict[str, Any]]:
        """Search for data science jobs using Tavily web search"""

        try:
            # Construct search queries for data science jobs
            search_queries = [
                f"data scientist jobs {location} {experience_years} year experience site:naukri.com",
                f"data analyst jobs {location} {experience_years} year experience site:linkedin.com",
                f"machine learning engineer jobs {location} {experience_years} year experience site:indeed.com",
                f"data science jobs {location} entry level {experience_years} year site:glassdoor.com",
                f"python data scientist {location} {experience_years} year experience site:shine.com"
            ]

            all_jobs = []

            for query in search_queries:
                try:
                    # Search using Tavily
                    search_results = self.tavily_client.search(
                        query=query,
                        search_depth="basic",
                        max_results=5,
                        include_domains=["naukri.com", "linkedin.com", "indeed.com", "glassdoor.com", "shine.com", "monster.com"]
                    )

                    # Process search results
                    for result in search_results.get('results', []):
                        job_data = self._extract_job_from_search_result(result, location, experience_years)
                        if job_data:
                            all_jobs.append(job_data)

                except Exception as e:
                    print(f"Error searching with query '{query}': {str(e)}")
                    continue

            # Remove duplicates and limit results
            unique_jobs = self._remove_duplicates(all_jobs)
            return unique_jobs[:max_results]

        except Exception as e:
            print(f"Error in job search: {str(e)}")
            # Return sample data science jobs as fallback
            return self._get_sample_data_science_jobs(location, experience_years)[:max_results]

    def _extract_job_from_search_result(self, result: Dict, location: str, experience: str) -> Optional[Dict]:
        """Extract job information from search result"""
        try:
            title = result.get('title', '')
            url = result.get('url', '')
            content = result.get('content', '')

            # Check if this looks like a data science job
            data_science_keywords = [
                'data scientist', 'data analyst', 'machine learning', 'data engineer',
                'business analyst', 'research analyst', 'python', 'sql', 'tableau',
                'power bi', 'statistics', 'analytics', 'big data', 'ai engineer'
            ]

            title_lower = title.lower()
            content_lower = content.lower()

            if not any(keyword in title_lower or keyword in content_lower for keyword in data_science_keywords):
                return None

            # Extract platform from URL
            platform = self._extract_platform(url)

            # Extract company name
            company = self._extract_company_name(title, content)

            # Extract salary
            salary = self._extract_salary(content)

            # Extract skills
            skills = self._extract_data_science_skills(content)

            # Extract experience requirement
            exp_required = self._extract_experience_requirement(content, experience)

            job_data = {
                "title": title,
                "company": company,
                "location": location,
                "experience_required": exp_required,
                "url": url,
                "platform": platform,
                "description": content[:500] + "..." if len(content) > 500 else content,
                "skills": skills,
                "salary": salary,
                "posted_date": "Recent",
                "job_type": "Full-time"
            }

            return job_data

        except Exception as e:
            print(f"Error extracting job data: {str(e)}")
            return None

    def _extract_platform(self, url: str) -> str:
        """Extract platform name from URL"""
        if 'naukri.com' in url:
            return 'Naukri'
        elif 'linkedin.com' in url:
            return 'LinkedIn'
        elif 'indeed.com' in url:
            return 'Indeed'
        elif 'glassdoor.com' in url:
            return 'Glassdoor'
        elif 'shine.com' in url:
            return 'Shine'
        elif 'monster.com' in url:
            return 'Monster'
        else:
            return 'Other'

    def _extract_company_name(self, title: str, content: str) -> str:
        """Extract company name from title or content"""
        # Common patterns for company names in job titles
        patterns = [
            r'at\s+([A-Z][a-zA-Z\s&]+?)(?:\s*-|\s*\||\s*$)',
            r'@\s*([A-Z][a-zA-Z\s&]+?)(?:\s*-|\s*\||\s*$)',
            r'([A-Z][a-zA-Z\s&]+?)\s*-\s*(?:hiring|jobs|careers)',
        ]

        for pattern in patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        # Fallback: look for company names in content
        company_indicators = ['company:', 'organization:', 'employer:']
        for indicator in company_indicators:
            if indicator in content.lower():
                start = content.lower().find(indicator) + len(indicator)
                end = content.find('\n', start)
                if end == -1:
                    end = start + 50
                company = content[start:end].strip()
                if company:
                    return company

        return "Company Name Not Available"

    def _extract_salary(self, content: str) -> Optional[str]:
        """Extract salary information from content"""
        # Indian salary patterns
        salary_patterns = [
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:to|-)?\s*₹?\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:LPA|per annum|annually)',
            r'(\d+(?:\.\d+)?)\s*(?:to|-)?\s*(\d+(?:\.\d+)?)\s*LPA',
            r'₹\s*(\d+(?:,\d+)*)\s*(?:to|-)?\s*₹?\s*(\d+(?:,\d+)*)',
            r'salary:?\s*₹?\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:to|-)?\s*₹?\s*(\d+(?:,\d+)*(?:\.\d+)?)',
        ]

        for pattern in salary_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(0)

        return None

    def _extract_data_science_skills(self, content: str) -> List[str]:
        """Extract data science related skills from content"""
        data_science_skills = [
            'Python', 'R', 'SQL', 'Tableau', 'Power BI', 'Excel', 'Pandas', 'NumPy',
            'Scikit-learn', 'TensorFlow', 'PyTorch', 'Keras', 'Matplotlib', 'Seaborn',
            'Jupyter', 'Statistics', 'Machine Learning', 'Deep Learning', 'NLP',
            'Computer Vision', 'Big Data', 'Hadoop', 'Spark', 'AWS', 'Azure', 'GCP',
            'Docker', 'Git', 'MongoDB', 'PostgreSQL', 'MySQL', 'Snowflake',
            'Databricks', 'Apache Airflow', 'MLflow', 'A/B Testing', 'ETL'
        ]

        found_skills = []
        content_lower = content.lower()

        for skill in data_science_skills:
            if skill.lower() in content_lower:
                found_skills.append(skill)

        return found_skills[:10]  # Limit to top 10 skills

    def _extract_experience_requirement(self, content: str, target_exp: str) -> str:
        """Extract experience requirement from content"""
        exp_patterns = [
            r'(\d+\+?\s*(?:to\s+\d+\s*)?years?\s*(?:of\s*)?experience)',
            r'(entry.level|junior|senior|mid.level)',
            r'(\d+\+?\s*years?)',
            r'experience:?\s*(\d+\+?\s*(?:to\s+\d+\s*)?years?)'
        ]

        for pattern in exp_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)

        return f"{target_exp} year experience"

    def _remove_duplicates(self, jobs: List[Dict]) -> List[Dict]:
        """Remove duplicate job postings"""
        seen = set()
        unique_jobs = []

        for job in jobs:
            # Create identifier based on title and company
            identifier = f"{job.get('title', '').lower()}_{job.get('company', '').lower()}"

            if identifier not in seen:
                seen.add(identifier)
                unique_jobs.append(job)

        return unique_jobs

    def _get_sample_data_science_jobs(self, location: str, experience: str) -> List[Dict]:
        """Fallback sample data science jobs"""
        return [
            {
                "title": "Data Scientist - Python & ML",
                "company": "Analytics Corp",
                "location": f"{location}, Karnataka",
                "experience_required": f"{experience} year",
                "url": "https://example.com/ds1",
                "platform": "Naukri",
                "description": "Looking for a data scientist with Python, ML, and statistical analysis skills",
                "skills": ["Python", "Machine Learning", "Statistics", "Pandas", "SQL"],
                "salary": "₹6-9 LPA",
                "posted_date": "1 day ago",
                "job_type": "Full-time"
            },
            {
                "title": "Business Data Analyst",
                "company": "Tech Insights Pvt Ltd",
                "location": f"{location}, Karnataka",
                "experience_required": f"{experience}-2 years",
                "url": "https://example.com/ds2",
                "platform": "LinkedIn",
                "description": "Business analyst role focusing on data analysis and reporting",
                "skills": ["SQL", "Tableau", "Excel", "Python", "Statistics"],
                "salary": "₹4-7 LPA",
                "posted_date": "2 days ago",
                "job_type": "Full-time"
            },
            {
                "title": "Junior Data Engineer",
                "company": "DataFlow Solutions",
                "location": f"{location}, Karnataka",
                "experience_required": f"{experience} year",
                "url": "https://example.com/ds3",
                "platform": "Indeed",
                "description": "Data engineer position working with ETL pipelines and big data",
                "skills": ["Python", "SQL", "Apache Spark", "AWS", "ETL"],
                "salary": "₹5-8 LPA",
                "posted_date": "3 days ago",
                "job_type": "Full-time"
            }
        ]


class DataScienceJobFilterTool(BaseTool):
    """Filter and score data science jobs based on specific criteria"""

    name: str = "data_science_job_filter_tool"
    description: str = "Filter and score data science jobs based on location, experience, and quality"

    def _run(self, jobs_data: List[Dict], location: str = "Bangalore",
             experience_years: str = "1") -> Dict[str, Any]:
        """Filter and score data science jobs"""

        filtered_jobs = []

        for job in jobs_data:
            # Location filter
            if location.lower() in job.get("location", "").lower():
                # Experience filter for data science roles
                if self._matches_experience_requirement(job, experience_years):
                    # Data science relevance filter
                    if self._is_data_science_relevant(job):
                        # Calculate quality score
                        job["quality_score"] = self._calculate_quality_score(job)

                        # Convert to Pydantic model for validation
                        try:
                            job_listing = JobListing(**job)
                            filtered_jobs.append(job_listing.dict())
                        except Exception as e:
                            print(f"Error validating job data: {e}")
                            # Add job anyway with default values
                            filtered_jobs.append(job)

        # Sort by quality score
        filtered_jobs.sort(key=lambda x: x.get("quality_score", 0), reverse=True)

        return {
            "filtered_jobs": filtered_jobs,
            "total_found": len(filtered_jobs),
            "location_filter": location,
            "experience_filter": f"{experience_years} year(s)",
            "job_categories": self._categorize_data_science_jobs(filtered_jobs)
        }

    def _matches_experience_requirement(self, job: Dict, target_exp: str) -> bool:
        """Check if job matches experience requirement"""
        exp_req = job.get("experience_required", "").lower()

        # Look for patterns that match the target experience
        target_patterns = [
            f"{target_exp} year",
            f"{target_exp}-",
            f"{target_exp}+",
            "entry level",
            "junior",
            "fresher"
        ]

        # Exclude senior positions
        senior_patterns = ["senior", "lead", "principal", "manager", "head", "director"]

        has_suitable_exp = any(pattern in exp_req for pattern in target_patterns)
        is_too_senior = any(pattern in exp_req for pattern in senior_patterns)

        return has_suitable_exp and not is_too_senior

    def _is_data_science_relevant(self, job: Dict) -> bool:
        """Check if job is relevant to data science field"""
        title = job.get("title", "").lower()
        description = job.get("description", "").lower()

        data_science_keywords = [
            "data scientist", "data analyst", "business analyst", "research analyst",
            "data engineer", "machine learning", "ml engineer", "ai engineer",
            "business intelligence", "analytics", "statistician", "quantitative analyst"
        ]

        return any(keyword in title or keyword in description for keyword in data_science_keywords)

    def _calculate_quality_score(self, job: Dict) -> int:
        """Calculate quality score for data science jobs"""
        score = 50  # Base score

        # Salary information
        if job.get("salary") and "₹" in job.get("salary", ""):
            score += 20

        # Skills relevance for data science
        skills = job.get("skills", [])
        data_science_core_skills = ["Python", "SQL", "Machine Learning", "Statistics", "Tableau", "R"]
        skill_matches = sum(1 for skill in skills if skill in data_science_core_skills)
        score += min(skill_matches * 5, 25)

        # Recent posting
        posted = job.get("posted_date", "").lower()
        if "1 day" in posted or "today" in posted:
            score += 15
        elif "2 day" in posted or "3 day" in posted:
            score += 10

        # Platform reputation for data science jobs
        platform_scores = {
            "linkedin": 15,
            "naukri": 12,
            "indeed": 10,
            "glassdoor": 8,
            "shine": 6
        }
        platform = job.get("platform", "").lower()
        score += platform_scores.get(platform, 5)

        # Description quality
        description_length = len(job.get("description", ""))
        if description_length > 300:
            score += 10
        elif description_length > 150:
            score += 5

        return min(score, 100)  # Cap at 100

    def _categorize_data_science_jobs(self, jobs: List[Dict]) -> Dict[str, List[Dict]]:
        """Categorize data science jobs by specialization"""
        categories = {
            "Data Scientist": [],
            "Data Analyst": [],
            "Business Analyst": [],
            "Data Engineer": [],
            "ML Engineer": [],
            "Research Analyst": [],
            "Other": []
        }

        for job in jobs:
            title = job.get("title", "").lower()

            if "data scientist" in title or "ml" in title or "machine learning" in title:
                categories["Data Scientist"].append(job)
            elif "data analyst" in title:
                categories["Data Analyst"].append(job)
            elif "business analyst" in title or "ba" in title:
                categories["Business Analyst"].append(job)
            elif "data engineer" in title or "etl" in title:
                categories["Data Engineer"].append(job)
            elif "ml engineer" in title or "ai engineer" in title:
                categories["ML Engineer"].append(job)
            elif "research analyst" in title or "quantitative" in title:
                categories["Research Analyst"].append(job)
            else:
                categories["Other"].append(job)

        return categories


class JobFilterTool:
    """Simple job filter tool without external dependencies"""

    def __init__(self):
        self.name = "job_filter_tool"
        self.description = "Filter jobs based on location and experience criteria"

    def run(self, jobs_data: List[Dict], location: str = "Bangalore",
            experience_years: str = "1") -> Dict[str, Any]:
        """Filter jobs based on specific criteria"""

        filtered_jobs = []

        for job in jobs_data:
            # Location filter
            if location.lower() in job.get("location", "").lower():
                # Experience filter
                exp_req = job.get("experience_required", "").lower()
                if (f"{experience_years} year" in exp_req or
                    f"{experience_years}-" in exp_req or
                    f"{experience_years}+" in exp_req):

                    # Add quality score
                    score = self._calculate_quality_score(job)
                    job["quality_score"] = score
                    filtered_jobs.append(job)

        # Sort by quality score
        filtered_jobs.sort(key=lambda x: x.get("quality_score", 0), reverse=True)

        return {
            "filtered_jobs": filtered_jobs,
            "total_found": len(filtered_jobs),
            "location_filter": location,
            "experience_filter": f"{experience_years} year(s)"
        }

    def _calculate_quality_score(self, job: Dict) -> int:
        """Calculate quality score for a job"""
        score = 50  # Base score

        # Salary information
        if job.get("salary") and job["salary"] != "Not specified":
            score += 20

        # Skills count
        skills_count = len(job.get("skills", []))
        score += min(skills_count * 3, 15)

        # Recent posting
        posted = job.get("posted_date", "").lower()
        if "1 day" in posted or "today" in posted:
            score += 15
        elif "2 day" in posted or "3 day" in posted:
            score += 10

        # Platform reputation
        platform_scores = {
            "linkedin": 10,
            "naukri": 8,
            "indeed": 7,
            "angellist": 9
        }
        platform = job.get("platform", "").lower()
        score += platform_scores.get(platform, 5)

        return min(score, 100)


