import requests
import time
import json
import re
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import Chrome<PERSON>river<PERSON>anager
from fake_useragent import UserAgent
from typing import List, Dict, Any
from crewai_tools import BaseTool
from pydantic import BaseModel, Field
import asyncio
import aiohttp


class JobSearchTool(BaseTool):
    name: str = "job_search_tool"
    description: str = "Search for job opportunities across multiple job platforms"
    
    def _run(self, search_terms: str, experience_level: str = "1-3 years", 
             location: str = "remote", max_results: int = 50) -> List[Dict[str, Any]]:
        """
        Search for jobs across multiple platforms
        """
        job_results = []
        
        # Job platforms to search
        platforms = {
            "indeed": self._search_indeed,
            "glassdoor": self._search_glassdoor,
            "linkedin": self._search_linkedin_jobs,
            "stackoverflow": self._search_stackoverflow,
        }
        
        for platform_name, search_func in platforms.items():
            try:
                print(f"Searching {platform_name}...")
                platform_results = search_func(search_terms, experience_level, location)
                job_results.extend(platform_results)
                time.sleep(2)  # Rate limiting
            except Exception as e:
                print(f"Error searching {platform_name}: {str(e)}")
                continue
        
        return job_results[:max_results]
    
    def _get_driver(self):
        """Setup Chrome driver with options"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        ua = UserAgent()
        chrome_options.add_argument(f"--user-agent={ua.random}")
        
        driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
            options=chrome_options
        )
        return driver
    
    def _search_indeed(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search Indeed for jobs"""
        jobs = []
        driver = self._get_driver()
        
        try:
            # Construct Indeed search URL
            base_url = "https://www.indeed.com/jobs"
            params = {
                "q": f"{search_terms} {experience_level}",
                "l": location,
                "fromage": "7",  # Last 7 days
                "sort": "date"
            }
            
            url = f"{base_url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            driver.get(url)
            
            # Wait for job cards to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-jk]"))
            )
            
            job_cards = driver.find_elements(By.CSS_SELECTOR, "[data-jk]")
            
            for card in job_cards[:20]:  # Limit to 20 per platform
                try:
                    title_elem = card.find_element(By.CSS_SELECTOR, "h2 a span")
                    company_elem = card.find_element(By.CSS_SELECTOR, "[data-testid='company-name']")
                    location_elem = card.find_element(By.CSS_SELECTOR, "[data-testid='job-location']")
                    link_elem = card.find_element(By.CSS_SELECTOR, "h2 a")
                    
                    job_data = {
                        "title": title_elem.text.strip(),
                        "company": company_elem.text.strip(),
                        "location": location_elem.text.strip(),
                        "url": "https://www.indeed.com" + link_elem.get_attribute("href"),
                        "platform": "Indeed",
                        "posted_date": "Recent"
                    }
                    jobs.append(job_data)
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"Error searching Indeed: {str(e)}")
        finally:
            driver.quit()
            
        return jobs
    
    def _search_glassdoor(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search Glassdoor for jobs"""
        jobs = []
        # Glassdoor implementation would go here
        # Note: Glassdoor has anti-bot measures, so this would need careful implementation
        return jobs
    
    def _search_linkedin_jobs(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search LinkedIn Jobs"""
        jobs = []
        driver = self._get_driver()
        
        try:
            # LinkedIn Jobs search URL
            base_url = "https://www.linkedin.com/jobs/search"
            params = {
                "keywords": f"{search_terms}",
                "location": location,
                "f_TPR": "r604800",  # Past week
                "f_E": "2"  # 1-2 years experience
            }
            
            url = f"{base_url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            driver.get(url)
            
            time.sleep(3)  # Wait for page load
            
            job_cards = driver.find_elements(By.CSS_SELECTOR, ".job-search-card")
            
            for card in job_cards[:15]:  # Limit results
                try:
                    title_elem = card.find_element(By.CSS_SELECTOR, ".base-search-card__title")
                    company_elem = card.find_element(By.CSS_SELECTOR, ".base-search-card__subtitle")
                    location_elem = card.find_element(By.CSS_SELECTOR, ".job-search-card__location")
                    link_elem = card.find_element(By.CSS_SELECTOR, ".base-card__full-link")
                    
                    job_data = {
                        "title": title_elem.text.strip(),
                        "company": company_elem.text.strip(),
                        "location": location_elem.text.strip(),
                        "url": link_elem.get_attribute("href"),
                        "platform": "LinkedIn",
                        "posted_date": "Recent"
                    }
                    jobs.append(job_data)
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"Error searching LinkedIn: {str(e)}")
        finally:
            driver.quit()
            
        return jobs
    
    def _search_stackoverflow(self, search_terms: str, experience_level: str, location: str) -> List[Dict]:
        """Search Stack Overflow Jobs"""
        jobs = []
        
        try:
            headers = {
                'User-Agent': UserAgent().random
            }
            
            # Stack Overflow Jobs API or web scraping
            url = f"https://stackoverflow.com/jobs?q={search_terms}&l={location}"
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                job_cards = soup.find_all('div', class_='listResults')
                
                for card in job_cards[:10]:
                    try:
                        title_elem = card.find('a', class_='s-link')
                        company_elem = card.find('span', class_='fc-black-700')
                        
                        if title_elem and company_elem:
                            job_data = {
                                "title": title_elem.text.strip(),
                                "company": company_elem.text.strip(),
                                "location": location,
                                "url": "https://stackoverflow.com" + title_elem.get('href', ''),
                                "platform": "Stack Overflow",
                                "posted_date": "Recent"
                            }
                            jobs.append(job_data)
                    except Exception as e:
                        continue
                        
        except Exception as e:
            print(f"Error searching Stack Overflow: {str(e)}")
            
        return jobs


class JobDataExtractorTool(BaseTool):
    name: str = "job_data_extractor"
    description: str = "Extract detailed information from job posting URLs"
    
    def _run(self, job_urls: List[str]) -> List[Dict[str, Any]]:
        """
        Extract detailed job information from URLs
        """
        extracted_jobs = []
        
        for url in job_urls:
            try:
                job_data = self._extract_job_details(url)
                if job_data:
                    extracted_jobs.append(job_data)
                time.sleep(1)  # Rate limiting
            except Exception as e:
                print(f"Error extracting from {url}: {str(e)}")
                continue
                
        return extracted_jobs
    
    def _extract_job_details(self, url: str) -> Dict[str, Any]:
        """Extract details from a single job posting"""
        headers = {
            'User-Agent': UserAgent().random
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Generic extraction logic - would need platform-specific implementations
            job_data = {
                "url": url,
                "title": self._extract_title(soup),
                "company": self._extract_company(soup),
                "location": self._extract_location(soup),
                "description": self._extract_description(soup),
                "requirements": self._extract_requirements(soup),
                "salary": self._extract_salary(soup),
                "benefits": self._extract_benefits(soup),
                "job_type": self._extract_job_type(soup),
                "experience_required": self._extract_experience(soup),
                "skills": self._extract_skills(soup)
            }
            
            return job_data
            
        except Exception as e:
            print(f"Error extracting job details: {str(e)}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract job title"""
        selectors = ['h1', '.jobsearch-JobInfoHeader-title', '[data-testid="jobTitle"]']
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text().strip()
        return "N/A"
    
    def _extract_company(self, soup: BeautifulSoup) -> str:
        """Extract company name"""
        selectors = ['.icl-u-lg-mr--sm', '[data-testid="inlineHeader-companyName"]', '.employer']
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text().strip()
        return "N/A"
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract job description"""
        selectors = ['#jobDescriptionText', '.jobsearch-jobDescriptionText', '.description']
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text().strip()[:1000]  # Limit length
        return "N/A"
    
    def _extract_requirements(self, soup: BeautifulSoup) -> List[str]:
        """Extract job requirements"""
        description = self._extract_description(soup)
        requirements = []
        
        # Look for common requirement patterns
        req_patterns = [
            r'(?:requirements?|qualifications?|must have)[:\s]*([^.]+)',
            r'(?:experience with|knowledge of|proficient in)[:\s]*([^.]+)',
            r'(?:\d+\+?\s*years?)[^.]*([^.]+)'
        ]
        
        for pattern in req_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            requirements.extend(matches)
        
        return requirements[:10]  # Limit to top 10
    
    def _extract_salary(self, soup: BeautifulSoup) -> str:
        """Extract salary information"""
        salary_patterns = [
            r'\$[\d,]+(?:\s*-\s*\$[\d,]+)?(?:\s*(?:per|/)\s*(?:year|hour|month))?',
            r'[\d,]+k?(?:\s*-\s*[\d,]+k?)?\s*(?:per|/)\s*(?:year|hour|month)'
        ]
        
        text = soup.get_text()
        for pattern in salary_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group()
        
        return "Not specified"
    
    def _extract_location(self, soup: BeautifulSoup) -> str:
        """Extract job location"""
        selectors = ['.jobsearch-JobInfoHeader-subtitle', '[data-testid="job-location"]']
        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text().strip()
        return "N/A"
    
    def _extract_benefits(self, soup: BeautifulSoup) -> List[str]:
        """Extract benefits information"""
        return []  # Simplified for now
    
    def _extract_job_type(self, soup: BeautifulSoup) -> str:
        """Extract job type (full-time, part-time, contract)"""
        text = soup.get_text().lower()
        if 'full-time' in text or 'full time' in text:
            return 'Full-time'
        elif 'part-time' in text or 'part time' in text:
            return 'Part-time'
        elif 'contract' in text:
            return 'Contract'
        return 'Not specified'
    
    def _extract_experience(self, soup: BeautifulSoup) -> str:
        """Extract experience requirements"""
        text = soup.get_text()
        exp_patterns = [
            r'(\d+\+?\s*(?:to\s+\d+\s*)?years?\s*(?:of\s*)?experience)',
            r'(entry.level|junior|senior|mid.level)',
            r'(\d+\+?\s*years?)'
        ]
        
        for pattern in exp_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "Not specified"
    
    def _extract_skills(self, soup: BeautifulSoup) -> List[str]:
        """Extract required skills"""
        text = soup.get_text().lower()
        
        # Common tech skills to look for
        skills_keywords = [
            'python', 'javascript', 'java', 'react', 'node.js', 'sql', 'aws', 
            'docker', 'kubernetes', 'git', 'agile', 'scrum', 'html', 'css',
            'machine learning', 'data science', 'tensorflow', 'pytorch',
            'angular', 'vue.js', 'mongodb', 'postgresql', 'redis', 'elasticsearch'
        ]
        
        found_skills = []
        for skill in skills_keywords:
            if skill in text:
                found_skills.append(skill)
        
        return found_skills[:15]  # Limit to top 15 skills
