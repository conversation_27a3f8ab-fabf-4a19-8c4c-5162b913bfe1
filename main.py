#!/usr/bin/env python3
"""
Job Scraping Agentic System
Main execution file for the job opportunity scraping and analysis system.
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crew import run_job_scraping_crew

def main():
    """Main function to run the job scraping system"""
    
    print("=" * 60)
    print("🤖 JOB OPPORTUNITY SCRAPING AGENTIC SYSTEM")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load environment variables
    load_dotenv()
    
    # Check if API key is available
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ Error: GOOGLE_API_KEY not found in environment variables.")
        print("Please set your Gemini API key in the .env file.")
        return
    
    print("✅ Environment variables loaded successfully")
    print("🔑 Gemini API key found")
    print()
    
    # Display system information
    print("🎯 SYSTEM CONFIGURATION:")
    print("- Framework: CrewAI with Gemini LLM")
    print("- Target Experience: 1+ years")
    print("- Platforms: Indeed, LinkedIn, Glassdoor")
    print("- Focus: Software Development, Data Science, Tech roles")
    print()
    
    # Ask user for custom search parameters
    print("🔧 SEARCH CONFIGURATION:")
    
    # Get user input for search terms
    search_terms = input("Enter search terms (default: 'software developer python'): ").strip()
    if not search_terms:
        search_terms = "software developer python"
    
    # Get location preference
    location = input("Enter location preference (default: 'remote'): ").strip()
    if not location:
        location = "remote"
    
    # Get maximum results
    try:
        max_results = input("Enter maximum results to process (default: 30): ").strip()
        max_results = int(max_results) if max_results else 30
    except ValueError:
        max_results = 30
    
    print()
    print("🚀 STARTING JOB SCRAPING PROCESS...")
    print(f"   Search Terms: {search_terms}")
    print(f"   Location: {location}")
    print(f"   Max Results: {max_results}")
    print()
    
    try:
        # Run the job scraping crew
        result = run_job_scraping_crew()
        
        if result:
            print("\n" + "=" * 60)
            print("✅ JOB SCRAPING COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print()
            print("📊 RESULTS:")
            print("- Comprehensive job analysis completed")
            print("- Report generated: career_opportunities_report.md")
            print("- Filtered opportunities based on 1+ years experience")
            print("- Quality scoring and categorization applied")
            print()
            print("📄 Next Steps:")
            print("1. Review the generated report")
            print("2. Check top-ranked opportunities")
            print("3. Follow application recommendations")
            print("4. Update your skills based on market trends")
            
        else:
            print("\n" + "=" * 60)
            print("❌ JOB SCRAPING FAILED")
            print("=" * 60)
            print("Please check the error messages above and try again.")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Process interrupted by user.")
        print("Exiting gracefully...")
        
    except Exception as e:
        print(f"\n❌ Unexpected error occurred: {str(e)}")
        print("Please check your configuration and try again.")
        
    finally:
        print(f"\n⏰ Process ended at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("Thank you for using the Job Scraping Agentic System! 🎉")


if __name__ == "__main__":
    main()
